<script lang="ts">
	import { page } from '$app/state';
	import { t } from '$lib/translations/config';

	import { navLinks } from './navLinks';

	let pillIndicatorElement: HTMLSpanElement;
	let previouslyActiveIndex = $state(-1);
	let activeIndex = $state(0);

	function updatePillIndicator(pathname: string) {
		if (!pillIndicatorElement) return;

		const activeLink = document.querySelector(
			`header nav a[href="${pathname}"]`
		) as HTMLAnchorElement;

		if (!activeLink) {
			previouslyActiveIndex = -1;
			activeIndex = -1;

			pillIndicatorElement.style.transition = 'opacity 0.2s ease';
			pillIndicatorElement.style.opacity = '0';

			return;
		}

		pillIndicatorElement.style.opacity = '1'; // Reset opacity style

		const index = activeLink.dataset.index ? parseInt(activeLink.dataset.index) : 0;

		const linkRect = activeLink.getBoundingClientRect();
		const navRect = activeLink.closest('.header-nav')?.getBoundingClientRect();

		let duration = 0.2;

		if (navRect) {
			const leftOffset = linkRect.left - navRect.left;
			const linkWidth = linkRect.width;

			pillIndicatorElement.style.left = `${leftOffset}px`;
			pillIndicatorElement.style.width = `${linkWidth}px`;
			pillIndicatorElement.style.transform = 'translateY(-50%)';

			// Don't animate for a first page load/pill select
			if (previouslyActiveIndex === -1) {
				pillIndicatorElement.style.transition = `left 0 ease, width 0 ease`;
			} else {
				const distance = Math.abs(index - previouslyActiveIndex);
				duration = Math.min(distance * 0.15, 0.3);
				pillIndicatorElement.style.transition = `left ${duration}s ease, width ${duration}s ease`;
			}
		}

		previouslyActiveIndex = index;

		activeIndex = -1;
		setTimeout(
			() => {
				activeIndex = index;
			},
			duration * 1000 - 500
		);
	}

	let innerWidth = $state(0);

	$effect(() => {
		if (innerWidth) {
			updatePillIndicator(page.url.pathname);
		}
	});
</script>

<svelte:window bind:innerWidth />

<nav class="header-nav">
	{#each navLinks as item, index (item.href)}
		<a href={item.href} data-index={index} class:active={index === activeIndex}>
			{$t(item.labelKey)}
		</a>
	{/each}

	<span class="pill-indicator" bind:this={pillIndicatorElement}></span>
</nav>

<style lang="scss">
	.header-nav {
		position: relative;
		width: 100%;
		background-color: var(--color-dark);
		height: var(--header-height);
		border-radius: calc(var(--header-height) / 2);
		display: flex;
		align-items: center;
		justify-content: space-between;
		isolation: isolate;
		padding: 0 0.6rem;

		--pill-height: calc(var(--header-height) - var(--spacing-s) * 2);

		a {
			color: var(--color-light);
			text-decoration: none;
			text-align: center;
			padding: 0 var(--spacing-m);
			height: var(--pill-height);
			width: max-content;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: calc(var(--pill-height) / 2);
			transition:
				background-color 0.2s ease,
				color 0.2s ease;

			&.active {
				color: var(--color-dark);
			}

			&:hover:not(.active) {
				background-color: rgba(255, 255, 255, 0.05);
			}
		}

		.pill-indicator {
			position: absolute;
			top: 50%;
			left: 0;
			width: 0;
			z-index: -1;
			opacity: 0;
			height: var(--pill-height);
			border-radius: calc(var(--pill-height) / 2);
			background-color: var(--color-light);
			transform: translateY(-50%);
		}
	}
</style>
