<script lang="ts">
	import csFlag from '$lib/assets/img/flags/cs.webp';
	import uaFlag from '$lib/assets/img/flags/ua.webp';
	import { Flex, Text } from '$lib/components';

	interface LanguageCellProps {
		language: string;
	}

	let { language }: LanguageCellProps = $props();

	const languages = [
		{ code: 'cs', name: '<PERSON><PERSON><PERSON><PERSON>', flag: csFlag },
		{ code: 'ua', name: 'Українська', flag: uaFlag }
	];

	const getLanguageName = (code: string): string => {
		const language = languages.find((l) => l.code === code);
		return language ? language.name : code.toUpperCase();
	};

	const getLanguageFlag = (code: string): string => {
		const language = languages.find((l) => l.code === code);
		return language ? language.flag : '';
	};
</script>

<Flex direction="row" align="center" gap="s">
	<img
		class="flag"
		src={getLanguageFlag(language)}
		alt="Flag of {getLanguageName(language)}"
	/>

	<Text>
		{getLanguageName(language)}
	</Text>
</Flex>

<style lang="scss">
	.flag {
		width: 1.25rem;
		height: 1.25rem;
		border-radius: 50%;
	}
</style>
