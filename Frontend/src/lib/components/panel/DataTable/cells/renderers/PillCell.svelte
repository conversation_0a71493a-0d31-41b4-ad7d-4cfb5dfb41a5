<script lang="ts">
	interface PillCellProps {
		hexColor: string;
		label: string;
	}

	let { hexColor, label }: PillCellProps = $props();
</script>

<span class="status-pill" style={`--pill-bg:${hexColor}30; --pill-fg:${hexColor}`}>
	{label}
</span>

<style lang="scss">
	.status-pill {
		display: inline-block;
		padding: 0.1rem 0.5rem;
		border-radius: 10em;
		font-size: 0.85em;
		font-weight: 600;
		text-transform: capitalize;
		background-color: var(--pill-bg);
		color: var(--pill-fg);
	}
</style>
