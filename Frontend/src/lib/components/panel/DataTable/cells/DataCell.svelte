<script lang="ts" generics="T_DATA, T_VALUE">
	import { exists } from '$lib/appmaxx/util';
	import { Text } from '$lib/components';

	import type { CellRenderer, DataCellProps } from '../DataTable.ts';

	let { cell }: DataCellProps<T_DATA, T_VALUE> = $props();

	const cellRenderer = $derived.by(() => {
		const cellDef = cell.column.columnDef.cell;

		if (typeof cellDef === 'string') {
			return cellDef;
		}

		if (typeof cellDef === 'function') {
			const result = cellDef(cell.getContext());

			if (!exists(result)) return '';

			if (typeof result === 'object' && 'component' in result && 'props' in result) {
				return result as CellRenderer;
			}

			return result;
		}

		return cellDef;
	});
</script>

<td class="data-cell">
	{#if typeof cellRenderer === 'object'}
		{@const Renderer = cellRenderer.component}
		{@const props = cellRenderer.props}

		<Renderer {...props} />
	{:else}
		<Text as="span" wrap="nowrap">
			{cellRenderer}
		</Text>
	{/if}
</td>

<style lang="scss">
	.data-cell {
		vertical-align: middle;
	}
</style>