<script lang="ts" generics="T_DATA">
	import './DataTable.scss';

	import { ChevronLeft, ChevronRight } from '@lucide/svelte';
	import {
		getCoreRowModel,
		getPaginationRowModel,
		getSortedRowModel,
		type PaginationState,
		type SortingState
	} from '@tanstack/table-core';

	import { Flex, Text } from '$lib/components';

	import DataCell from './cells/DataCell.svelte';
	import HeaderCell from './cells/HeaderCell.svelte';
	import { type DataTableProps, getPaginationStructure } from './DataTable';
	import { createSvelteTable } from './DataTable.svelte.js';

	let { data, columns }: DataTableProps<T_DATA> = $props();

	let pagination = $state<PaginationState>({
		pageIndex: 0,
		pageSize: 20
	});
	let sorting = $state<SortingState>([]);

	const table = createSvelteTable({
		get data() {
			return data;
		},
		get columns() {
			return columns;
		},
		state: {
			get pagination() {
				return pagination;
			},
			get sorting() {
				return sorting;
			}
		},
		getCoreRowModel: getCoreRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		onSortingChange: (updater) => {
			if (typeof updater === 'function') {
				sorting = updater(sorting);
			} else {
				sorting = updater;
			}
		},
		onPaginationChange: (updater) => {
			if (typeof updater === 'function') {
				pagination = updater(pagination);
			} else {
				pagination = updater;
			}
		}
	});

	const headerGroups = $derived(table.getHeaderGroups());
	const rows = $derived(table.getRowModel().rows);
	const isEmpty = $derived(rows.length === 0);
	const paginationStructure = $derived(
		getPaginationStructure(pagination.pageIndex + 1, table.getPageCount())
	);
</script>

<div class="data-table-container">
	<div class="table-scroll-wrapper">
		<table class="data-table">
			<thead>
				{#each headerGroups as headerGroup (headerGroup.id)}
					<tr>
						{#each headerGroup.headers as header (header.id)}
							<th>
								{#if !header.isPlaceholder}
									<HeaderCell {header} />
								{/if}
							</th>
						{/each}
					</tr>
				{/each}
			</thead>

			<tbody>
				{#each rows as row (row.id)}
					<tr>
						{#each row.getVisibleCells() as cell (cell.id)}
							<DataCell {cell} />
						{/each}
					</tr>
				{:else}
					<tr>
						<td colspan={columns.length} class="empty-state">
							<div class="empty-message">No data available</div>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>
	</div>

	{#if !isEmpty}
		<div class="table-pagination">
			<Flex direction="row" gap="s">
				<button
					class="pagination-btn"
					disabled={pagination.pageIndex === 0}
					onclick={() => table.setPageIndex(pagination.pageIndex - 1)}
				>
					<ChevronLeft size="1.5em" />
				</button>

				{#each paginationStructure as page, i (i)}
					{#if typeof page === 'string'}
						<Text as="span" color="secondary">{page}</Text>
					{:else if typeof page === 'number'}
						<button
							class="pagination-btn"
							class:active={page === pagination.pageIndex + 1}
							onclick={() => table.setPageIndex(page - 1)}
						>
							{page}
						</button>
					{/if}
				{/each}

				<button
					class="pagination-btn"
					disabled={pagination.pageIndex === table.getPageCount() - 1}
					onclick={() => table.setPageIndex(pagination.pageIndex + 1)}
				>
					<ChevronRight size="1.5em" />
				</button>
			</Flex>
		</div>
	{/if}
</div>
