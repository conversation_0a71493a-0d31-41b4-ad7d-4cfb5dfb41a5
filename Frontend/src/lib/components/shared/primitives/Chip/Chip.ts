import type { Component } from 'svelte';
import type { HTMLAttributes } from 'svelte/elements';

import { cva, type ResponsiveType } from '$lib/appmaxx/cva';
import type { Actionable } from '$lib/appmaxx/svelte-actions';
import type { Childable } from '$lib/components/cva-constants';

const variants = {
	variant: {
		light: 'chip-variant-light',
		dark: 'chip-variant-dark',
		primary: 'chip-variant-primary',
		error: 'chip-variant-error'
	},
	size: {
		small: 'chip-size-small',
		medium: 'chip-size-medium',
		large: 'chip-size-large'
	}
};

const defaultVariants = {
	variant: 'light',
	size: 'medium'
} as const;

type ChipVariantProps = {
	variant?: keyof typeof variants.variant;
	size?: ResponsiveType<keyof typeof variants.size>;
};

export const chipCva = cva<ChipVariantProps>('chip', variants, defaultVariants);

type CustomChipProps = {
	Icon?: Component;
};

export type ChipProps = HTMLAttributes<HTMLDivElement> &
	ChipVariantProps &
	CustomChipProps &
	Actionable &
	Childable;
