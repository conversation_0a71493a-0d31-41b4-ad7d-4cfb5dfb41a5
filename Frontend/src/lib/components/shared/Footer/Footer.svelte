<script lang="ts">
	import { Facebook, Instagram, Mail, Phone } from '@lucide/svelte';
	import type { Component } from 'svelte';

	import { page } from '$app/state';
	import appmaxxingLogo from '$lib/assets/img/logos/appmaxxing-logo.svg';
	import logo from '$lib/assets/img/logos/logo.svg';
	import tiktok from '$lib/assets/img/social-icons/tik-tok.svg';
	import {
		Flex,
		Grid,
		Heading,
		Image,
		PresentationResponsiveContainer,
		Spacer,
		Text,
		TextLink
	} from '$lib/components';
	import { t } from '$lib/translations/config';

	let isInPanel = $derived(page.url.pathname.includes('/panel'));
</script>

{#snippet socialIcon(src: string, Icon: Component, name: string, href: string)}
	<Flex direction="row" gap="s" align="end">
		{#if src}
			<Image height="18px" {src} alt={name} />
		{/if}

		{#if Icon}
			<Icon size="1.1em" strokeWidth={2} />
		{/if}

		<Text as="span">
			<TextLink {href} underlineVariant="dark">
				{name}
			</TextLink>
		</Text>
	</Flex>
{/snippet}

<footer class:panel-layout={isInPanel}>
	<PresentationResponsiveContainer>
		<Grid
			cols={{ mobile: '1', tablet: '2', desktop: '4' }}
			gap="xl"
			paddingY="xxl"
			paddingX={{ mobile: 'm', tablet: 'none', desktop: 'none' }}
		>
			<Flex direction="col" align="start">
				<Flex direction="row" gap="sm" align="center">
					<Image src={logo} alt="Logo" height="36px" />
					<Heading as="h2" size="4">{$t('footer.companyInfo.companyName')}</Heading>
				</Flex>
				<Spacer direction="vertical" size="m" />
				<Text as="span">{$t('footer.companyInfo.address')}</Text>
				<Spacer direction="vertical" size="s" />

				<Text as="span">{$t('footer.companyInfo.city')}</Text>
				<Spacer direction="vertical" size="s" />

				<Text as="span">{$t('footer.companyInfo.ico')}</Text>
				<Spacer direction="vertical" size="s" />
			</Flex>

			<Flex direction="col" align="start">
				<Heading as="h2" size="4">{$t('footer.socialMedia.title')}</Heading>
				<Spacer direction="vertical" size="m" />

				{@render socialIcon(
					undefined,
					Instagram,
					$t('footer.socialMedia.instagram'),
					$t('footer.socialMedia.instagramLink')
				)}
				<Spacer direction="vertical" size="sm" />

				{@render socialIcon(
					undefined,
					Facebook,
					$t('footer.socialMedia.facebook'),
					$t('footer.socialMedia.facebookLink')
				)}
				<Spacer direction="vertical" size="sm" />

				{@render socialIcon(
					tiktok,
					undefined,
					$t('footer.socialMedia.tiktok'),
					$t('footer.socialMedia.tiktokLink')
				)}
				<Spacer direction="vertical" size="sm" />
			</Flex>

			<Flex direction="col" align="start">
				<Heading as="h2" size="4">{$t('footer.contact.title')}</Heading>
				<Spacer direction="vertical" size="m" />

				{#snippet contactIcon(Icon: Component, name: string, href: string)}
					<Flex direction="row" gap="s" align="center">
						<Icon size="1.1em" strokeWidth={2} />
						<Text as="span">
							<TextLink {href} underlineVariant="dark">
								{name}
							</TextLink>
						</Text>
					</Flex>
				{/snippet}

				{@render contactIcon(
					Phone,
					$t('footer.contact.phone'),
					$t('footer.contact.phoneLink')
				)}
				<Spacer direction="vertical" size="sm" />

				{@render contactIcon(
					Mail,
					$t('footer.contact.email'),
					$t('footer.contact.emailLink')
				)}
			</Flex>

			<Flex direction="col" align={{ mobile: 'start', tablet: 'start', desktop: 'end' }}>
				<Heading as="h2" size="4">{$t('footer.usefulLinks.title')}</Heading>
				<Spacer direction="vertical" size="m" />
				<TextLink href="/o-nas" underlineVariant="dark">
					{$t('footer.usefulLinks.whoWeAre')}
				</TextLink>
				<Spacer direction="vertical" size="sm" />
				<TextLink href="/cenik" underlineVariant="dark">
					{$t('footer.usefulLinks.pricing')}
				</TextLink>
				<Spacer direction="vertical" size="sm" />
				<TextLink href="/pridej-se-k-nam" underlineVariant="dark">
					{$t('footer.usefulLinks.becomeTeacher')}
				</TextLink>
				<Spacer direction="vertical" size="sm" />
				<TextLink href="/kontakt" underlineVariant="dark">
					{$t('footer.usefulLinks.contacts')}
				</TextLink>
			</Flex>
		</Grid>
	</PresentationResponsiveContainer>

	<div class="copyright-container">
		<PresentationResponsiveContainer>
			<Grid cols={{ mobile: '1', tablet: '2', desktop: '2' }} gap="xl" paddingY="xl">
				<Flex
					justify={{ mobile: 'center', tablet: 'start', desktop: 'start' }}
					align="center"
					gap={{ mobile: 's', tablet: 'l', desktop: 'l' }}
					direction={{ mobile: 'col', tablet: 'row', desktop: 'row' }}
				>
					<Text as="span">
						<TextLink href="/obchodni-podminky" underlineVariant="dark">
							{$t('footer.copyright.termsOfService')}
						</TextLink>
					</Text>

					<Text as="span">
						<TextLink href="/zasady-ochrany-osobnich-udaju" underlineVariant="dark">
							{$t('footer.copyright.privacyPolicy')}
						</TextLink>
					</Text>
				</Flex>

				<Flex
					align={{ mobile: 'center', tablet: 'end', desktop: 'end' }}
					direction="col"
					gap="s"
				>
					<Text as="span">{$t('footer.copyright.rights')}</Text>

					<Flex direction="row" gap="s" align="center">
						<Image src={appmaxxingLogo} alt="Appmaxxing logo" height="22px" />
						<Text as="span" lineHeight="1">
							Made by

							<TextLink href="https://appmaxxing.cz" underlineVariant="dark">
								Appmaxxing s.r.o.
							</TextLink>
						</Text>
					</Flex>
				</Flex>
			</Grid>
		</PresentationResponsiveContainer>
	</div>
</footer>

<style lang="scss">
	footer {
		background-color: var(--color-dark);
		color: var(--color-light);
		margin-top: auto;

		&.panel-layout {
			padding-left: 250px;
		}

		.copyright-container {
			border-top: 1px solid var(--color-border-dark);
		}
	}
</style>
