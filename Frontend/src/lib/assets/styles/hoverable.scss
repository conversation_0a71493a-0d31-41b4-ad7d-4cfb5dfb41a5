@mixin hoverable($padding: 0.25rem) {
	position: relative;
	isolation: isolate;

	&:before {
		content: '';
		position: absolute;
		z-index: -1;
		top: calc($padding * -1);
		bottom: calc($padding * -1);
		left: calc($padding * -1);
		right: calc($padding * -1);
		background-color: rgba(25, 25, 25, 0.05);
		border-radius: var(--border-radius-s);
		transition: opacity 0.2s ease;
		opacity: 0;
	}

	&:hover:before,
	&:focus-within:before,
	&:active:before,
	&:focus-visible:before {
		opacity: 1;
	}
}
