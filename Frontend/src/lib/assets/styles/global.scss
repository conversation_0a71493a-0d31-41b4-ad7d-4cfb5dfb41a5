@use '_reset.scss';

html {
	font-family: 'Ubuntu', sans-serif;
	color-scheme: light;
	scroll-behavior: smooth;
}

body {
	background-color: var(--color-light);
	color: var(--color-dark);
	font-size: 16px;
	min-height: 100dvh;
	display: flex;
	flex-direction: column;
	font-synthesis: none;
	text-rendering: optimizeLegibility;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	overflow-x: hidden;
}

:root {
	--color-primary: #427ef4;
	--color-light: #ffffff;
	--color-dark: #1e1e1e;
	--color-icon-dark: #2d2d2d;
	--color-section: #f6f6f6;
	--color-border-light: #e6e6e6;
	--color-border-dark: #303030;
	--color-secondary-text: #868686;
	--color-success: #2ecc71;
	--color-error: #f44242;

	--color-primary-hover: #3364c4;
	--color-light-hover: #eeeeee;
	--color-dark-hover: #282828;
	--color-success-hover: #21b05f;
	--color-error-hover: #c53636;

	--focus-outline-color: var(--color-primary);
	--focus-box-shadow-color: white;

	// Font sizes
	--font-size-hero: 3.75rem; // 60px
	--font-size-1: 3rem; // 48px
	--font-size-2: 2rem; // 32px
	--font-size-3: 1.5rem; // 24px
	--font-size-4: 1.125rem; // 18px
	--font-size-text: 0.875rem; // 14px
	--font-size-small: 0.75rem; // 12px
	--font-size-sub: 0.625rem; // 10px

	// Spacings
	--spacing-xs: 0.25rem; // 4px
	--spacing-s: 0.5rem; // 8px
	--spacing-sm: 0.75rem; // 12px
	--spacing-m: 1rem; // 16px
	--spacing-ml: 1.25rem; // 20px
	--spacing-l: 1.5rem; // 24px
	--spacing-xl: 2rem; // 32px
	--spacing-xxl: 3rem; // 48px
	--spacing-between-sections-half: 6rem; // 64px
	--spacing-between-sections: 8rem; // 128px

	// Page paddings
	--mobile-side-padding: 1.25rem; // 20px
	--tablet-side-padding: 2rem; // 32px
	--desktop-side-padding: 4rem; // 64px
	--max-content-width: 1380px;

	// Border radii
	--border-radius-s: 0.5rem; // 8px
	--border-radius-m: 1rem; // 16px
	--border-radius-l: 1.5rem; // 24px
	--border-radius-xl: 2rem; // 32px

	--header-height: 3.5rem;

	// Media query breakpoints
	--breakpoint-mobile-max: 639px;
	--breakpoint-tablet-min: 640px;
	--breakpoint-tablet-max: 1023px;
	--breakpoint-desktop-min: 1024px;

	// Z-indexes
	--z-index-popover: 30;
	--z-index-header: 10;
	--z-index-header-shadow: 9;
	--z-index-mobile-nav: 20;
	--side-disclosure-z-index: 1;
}

// Global styles for render-transformed translations
.translations {
	&-link {
		text-decoration: underline;
		color: inherit;

		&:hover {
			color: var(--color-primary);
		}
	}

	&-primary {
		color: var(--color-primary);
	}

	&-secondary {
		color: var(--color-secondary-text);
	}

	&-semibold {
		font-weight: 500;
	}

	&-semibold-dark {
		font-weight: 500;
		color: var(--color-dark);
	}
}

// Screen reader only
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}

// Lucide icons
.lucide-icon {
	flex-shrink: 0;
}

// Disable transitions and animations for users with reduced motion
@media (prefers-reduced-motion: reduce) {
	* {
		transition: none !important;
		animation: none !important;
	}
}

// Focus style -> on TAB on any focusable element
:focus-visible:not([popover]):not(input):not(textarea) {
	outline-offset: 1px !important;
	outline: 3px solid var(--focus-outline-color) !important;
	box-shadow: 0 0 0 5px var(--focus-box-shadow-color) !important;
}
