import { createMongoAbility, type RawRuleOf } from '@casl/ability';
import { redirect } from '@sveltejs/kit';

import type { FetchLike } from '$lib/api/apiServiceWrapper';
import { exists } from '$lib/appmaxx/util';
import type {
	Action,
	AppAbility,
	AppTuple,
	Ruleset,
	Subject,
	UserRole
} from '$lib/permissions/types';

const rulesetCache: {
	data: Ruleset | null;
	lastFetched: number;
	ttl: number;
} = {
	data: null,
	lastFetched: 0,
	ttl: 5 * 60 * 1000 // 5 minutes in milliseconds
};

export async function getCachedRuleset(fetch: FetchLike): Promise<Ruleset | null> {
	const now = Date.now();

	// Check if the cache is valid
	if (rulesetCache.data && now - rulesetCache.lastFetched < rulesetCache.ttl) {
		return rulesetCache.data;
	}

	// Cache is expired or doesn't exist, fetch new data
	const ruleset = await fetchRuleset(fetch);

	// Update cache
	rulesetCache.data = ruleset;
	rulesetCache.lastFetched = now;

	return ruleset;
}

export function getRulesForRole(ruleset: Ruleset | null, role: UserRole): RawRuleOf<AppAbility>[] {
	const perms = exists(ruleset) ? ruleset[role] : {};

	const rules: RawRuleOf<AppAbility>[] = [];

	for (const [subject, actions] of Object.entries(perms)) {
		if (!actions) continue;

		for (const [action, allowed] of Object.entries(actions) as [Action, boolean][]) {
			if (allowed) {
				rules.push({ action, subject } as RawRuleOf<AppAbility>);
			}
		}
	}

	return rules;
}

export function buildAbilitiesFromRules(rules: RawRuleOf<AppAbility>[]): AppAbility {
	return createMongoAbility<AppTuple>(rules) as AppAbility;
}

export function getUserRoleTranslationKey(role: UserRole): string {
	return `roles.${role}`;
}

export function getUserRoleColor(role: UserRole): string {
	switch (role) {
		case 'admin':
			return '#ff2276';
		case 'manager':
			return '#309ade';
		case 'teacher':
			return '#27cb6b';
		case 'student':
			return '#ea8121';
		default:
			return '#95A5A6';
	}
}

export function guardRoute(
	rules: RawRuleOf<AppAbility>[] | null | undefined,
	subject: Subject | Subject[],
	action: Action | Action[],
	redirectTo: string = '/panel'
) {
	const ability = buildAbilitiesFromRules(rules ?? []);

	if (!can(ability, subject, action)) {
		throw redirect(302, redirectTo);
	}
}

export function can(
	ability: AppAbility | null | undefined,
	subject: Subject | Subject[],
	action: Action | Action[]
): boolean {
	if (!exists(ability)) return false;

	const actions = Array.isArray(action) ? action : [action];
	const subjects = Array.isArray(subject) ? subject : [subject];

	return actions.some((a) => subjects.some((s) => ability.can(a, s)));
}

async function fetchRuleset(fetch: FetchLike): Promise<Ruleset | null> {
	const s2sSecret = process.env.S2S_SECRET!;

	try {
		const res = await fetch('http://backend:3000/auth/permissions', {
			headers: {
				Authorization: `Bearer ${s2sSecret}`
			}
		});

		const { ruleset }: { ruleset: Ruleset } = await res.json();
		return ruleset;
	} catch (error) {
		console.error('Failed to fetch permissions:', error);
		return null;
	}
}
