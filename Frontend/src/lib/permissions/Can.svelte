<script lang="ts">
	import type { Snippet } from 'svelte';

	import { type Action, can, type Subject } from '$lib/permissions';
	import { abilities } from '$lib/stores/user';

	interface Props {
		action: Action | Action[];
		subject: Subject | Subject[];
		field?: string;
		children?: Snippet;
	}

	let { action, subject, field, children }: Props = $props();

	const canAccess = $derived(can($abilities, subject, action));
</script>

{#if canAccess}
	{@render children?.()}
{/if}
