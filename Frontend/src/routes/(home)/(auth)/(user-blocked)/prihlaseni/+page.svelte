<script lang="ts">
	import { KeyRound, Mail } from '@lucide/svelte';
	import { defaults, superForm } from 'sveltekit-superforms';
	import { zod4 } from 'sveltekit-superforms/adapters';

	import { goto, invalidate } from '$app/navigation';
	import { page } from '$app/state';
	import { apiService, applyBackendToForm } from '$lib/api';
	import { Button, Flex, Heading, Input, MetaSeo, Spacer, Text } from '$lib/components';
	import { LoginSchema } from '$lib/schemas';
	import { T, t } from '$lib/translations/config';

	const data = defaults(zod4(LoginSchema));
	const { form, errors, enhance, message } = superForm(data, {
		validators: zod4(LoginSchema),
		SPA: true,
		taintedMessage: null,
		resetForm: false,
		clearOnSubmit: 'errors-and-message',
		onUpdate: async ({ form }) => {
			if (!form.valid) return;

			const response = await apiService.domains.auth.login(form.data);

			if (!response.ok) {
				form.valid = false;
				form.message = 'global.errors.internal';
			}
			applyBackendToForm(form, response);

			if (form.valid) {
				await invalidate('app:user');
				const next = page.url.searchParams.get('next') ?? '/panel';
				await goto(next);
			}
		}
	});
</script>

<MetaSeo
	title={$t('login.pageTitle')}
	description={$t('login.pageDescription')}
	robots="index, follow"
/>

<Flex flex="1" direction="col" gap={{ mobile: 'm', tablet: 'l', desktop: 'l' }}>
	<Heading size={{ mobile: '3', tablet: '2', desktop: '2' }}>
		<T key="login.title"></T>
	</Heading>

	<form method="POST" use:enhance autocomplete="on">
		<Flex direction="col" gap="l">
			<Flex direction="col" gap="s">
				<Flex direction="col" gap="s">
					<Input
						id="email"
						type="email"
						placeholder={$t('login.form.email.label')}
						autocomplete="email"
						Icon={Mail}
						errors={$errors.email}
						bind:value={$form.email}
					/>

					<Input
						id="password"
						type="password"
						placeholder={$t('login.form.password.label')}
						autocomplete="current-password"
						Icon={KeyRound}
						errors={$errors.password}
						bind:value={$form.password}
					/>
				</Flex>

				<Text align="right">
					<T key="login.forgotPassword"></T>
				</Text>
			</Flex>

			<Flex direction="col">
				{#if $message}
					<Text role="alert" style="color: crimson;">
						<T key={$message}></T>
					</Text>
				{/if}
				<Spacer direction="vertical" size="m" />

				<Button type="submit">
					<T key="login.form.submit"></T>
				</Button>
				<Spacer direction="vertical" size="l" />

				<Text align="center">
					<T key="login.notRegistered"></T>
				</Text>
			</Flex>
		</Flex>
	</form>
</Flex>

<style lang="scss">
	form {
		width: 100%;
		height: 100%;
	}
</style>
