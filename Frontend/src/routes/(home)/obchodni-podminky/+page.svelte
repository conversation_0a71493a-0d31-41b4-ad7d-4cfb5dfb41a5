<script lang="ts">
	import { Heading, MarkdownStyles, MetaSeo, Spacer } from '$lib/components';
	import { T, t, TMd } from '$lib/translations/config';
</script>

<MetaSeo
	title={$t('tos.pageTitle')}
	description={$t('tos.pageDescription')}
	robots="index, follow"
/>

<Spacer direction="vertical" size="xxl" />

<Heading as="h1" size={{ mobile: '1', tablet: 'hero', desktop: 'hero' }} align="center">
	<T key="tos.hero" />
</Heading>

<Spacer direction="vertical" size="between-sections" />

<MarkdownStyles>
	<TMd key="tosMd" />
</MarkdownStyles>
