<script lang="ts">
	import students from '$lib/assets/img/page-specific/auth/students.webp';
	import { MetaSeo } from '$lib/components';
	import TutoringPageContainer from '$lib/components/presentation/TutoringPageContainer.svelte';
	import { t } from '$lib/translations/config';
</script>

<MetaSeo
	title={$t('tutoringOnline.pageTitle')}
	description={$t('tutoringOnline.pageDescription')}
	robots="index, follow"
/>

<TutoringPageContainer
	heading={$t('tutoringOnline.hero.title')}
	chips={[
		$t('tutoringOnline.hero.chips.1'),
		$t('tutoringOnline.hero.chips.2'),
		$t('tutoringOnline.hero.chips.3')
	]}
	imageSrc={students}
	heroDescription={$t('tutoringOnline.hero.description')}
	action={$t('tutoringOnline.hero.action')}
	pageDescription={$t('tutoringOnline.description')}
	featureCardKeyStart="tutoringOnline.perks"
	comingSoon={$t('tutoringOnline.comingSoon')}
/>
