<script lang="ts">
	import students from '$lib/assets/img/page-specific/auth/students.webp';
	import { MetaSeo } from '$lib/components';
	import TutoringPageContainer from '$lib/components/presentation/TutoringPageContainer.svelte';
	import { t } from '$lib/translations/config';
</script>

<MetaSeo
	title={$t('tutoringPresence.pageTitle')}
	description={$t('tutoringPresence.pageDescription')}
	robots="index, follow"
/>

<TutoringPageContainer
	heading={$t('tutoringPresence.hero.title')}
	chips={[
		$t('tutoringPresence.hero.chips.1'),
		$t('tutoringPresence.hero.chips.2'),
		$t('tutoringPresence.hero.chips.3')
	]}
	heroDescription={$t('tutoringPresence.hero.description')}
	action={$t('tutoringPresence.hero.action')}
	imageSrc={students}
	pageDescription={$t('tutoringPresence.description')}
	featureCardKeyStart="tutoringPresence.perks"
	comingSoon={$t('tutoringPresence.comingSoon')}
	map={true}
	mapTitle={$t('tutoringPresence.map.title')}
	mapSubtitle={$t('tutoringPresence.map.subtitle')}
/>
