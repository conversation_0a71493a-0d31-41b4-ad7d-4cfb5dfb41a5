<script lang="ts">
	import students from '$lib/assets/img/page-specific/auth/students.webp';
	import { MetaSeo } from '$lib/components';
	import TutoringPageContainer from '$lib/components/presentation/TutoringPageContainer.svelte';
	import { t } from '$lib/translations/config';
</script>

<MetaSeo
	title={$t('tutoringIndividual.pageTitle')}
	description={$t('home.tutoringIndividual')}
	robots="index, follow"
/>

<TutoringPageContainer
	heading={$t('tutoringIndividual.hero.title')}
	chips={[
		$t('tutoringIndividual.hero.chips.1'),
		$t('tutoringIndividual.hero.chips.2'),
		$t('tutoringIndividual.hero.chips.3')
	]}
	heroDescription={$t('tutoringIndividual.hero.description')}
	action={$t('tutoringIndividual.hero.action')}
	pageDescription={$t('tutoringIndividual.description')}
	imageSrc={students}
	featureCardKeyStart="tutoringIndividual.perks"
	comingSoon={$t('tutoringIndividual.comingSoon')}
/>
