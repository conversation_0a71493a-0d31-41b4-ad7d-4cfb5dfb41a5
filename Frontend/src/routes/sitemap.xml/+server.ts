// src/routes/sitemap.xml/+server.ts

import type { RequestHandler } from '@sveltejs/kit';

// Dynamic by default; opt out of prerender so it’s always fresh at request time.
export const prerender = false;

function escapeXml(s: string) {
	return s.replace(/[<>&'"]/g, (c) =>
		c === '<'
			? '&lt;'
			: c === '>'
				? '&gt;'
				: c === '&'
					? '&amp;'
					: c === '"'
						? '&quot;'
						: '&apos;'
	);
}

// Platform-agnostic SHA-256 for ETag (works on Node & Edge)
async function sha256Hex(text: string) {
	if (globalThis.crypto?.subtle) {
		const buf = new TextEncoder().encode(text);
		const digest = await crypto.subtle.digest('SHA-256', buf);
		return [...new Uint8Array(digest)].map((b) => b.toString(16).padStart(2, '0')).join('');
	} else {
		const { createHash } = await import('node:crypto');
		return createHash('sha256').update(text).digest('hex');
	}
}

type UrlEntry = {
	loc: string; // absolute URL (no trailing slash mismatch)
	lastmod?: string; // ISO8601: 2025-09-01T12:34:56+00:00
	// changefreq/priority intentionally omitted (Google largely ignores them)
};

async function getUrls(): Promise<UrlEntry[]> {
	// TODO: replace with your DB/CMS queries
	// Keep URLs consistent with your trailingSlash/base config
	const base = process.env.FRONTEND_URL?.replace(/\/+$/, '');
	const now = new Date().toISOString();

	// Example: fetch published slugs from your DB and map to URLs
	const slugs = [
		'/',
		'/cenik',
		'/individualni-doucovani',
		'/kontakt',
		'/o-nas',
		'/obchodni-podminky',
		'/online-doucovani',
		'/prezencni-doucovani',
		'/prijimacky-nanecisto',
		'/prijimacky-osmileta-gymnazia',
		'/prijimacky-ss',
		'/pridej-se-k-nam',
		'/zasady-ochrany-osobnich-udaju'
	];
	return slugs.map((p) => ({
		loc: base + (p.startsWith('/') ? p : `/${p}`),
		lastmod: '2025-09-01T21:44:02.228Z'
	}));
}

function renderSitemap(urls: UrlEntry[]) {
	const rows = urls
		.map(
			(u) => `
  <url>
    <loc>${escapeXml(u.loc)}</loc>${
		u.lastmod
			? `
    <lastmod>${u.lastmod}</lastmod>`
			: ''
	}
  </url>`
		)
		.join('');

	return `<?xml version="1.0" encoding="UTF-8"?>
<urlset
  xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
  xmlns:xhtml="http://www.w3.org/1999/xhtml"
>
${rows}
</urlset>`;
}

export const GET: RequestHandler = async ({ request, setHeaders }) => {
	const urls = await getUrls();
	const xml = renderSitemap(urls);

	// Strong caching at the edge, 10 min TTL, serve stale for a day while revalidating
	setHeaders({
		'content-type': 'application/xml; charset=utf-8',
		'cache-control': 'public, max-age=0, s-maxage=600, stale-while-revalidate=86400'
	});

	// Manual ETag (useful across Node/Edge; avoids recomputing body downstream)
	const etag = `W/"${await sha256Hex(xml)}"`;
	setHeaders({ etag });

	if (request.headers.get('if-none-match') === etag) {
		return new Response(null, { status: 304 });
	}

	return new Response(xml);
};
