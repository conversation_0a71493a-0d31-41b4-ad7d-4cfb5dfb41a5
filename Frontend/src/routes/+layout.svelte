<script lang="ts">
	import '$lib/assets/styles/global.scss';

	import { QueryClient, QueryClientProvider } from '@tanstack/svelte-query';
	import { onMount } from 'svelte';

	import { browser } from '$app/environment';
	import { afterNavigate, goto, invalidate } from '$app/navigation';
	import { page } from '$app/state';
	import { setTranslationsContext } from '$lib/appmaxx/translations';
	import appleTouchIcon from '$lib/assets/img/logos/apple-touch-icon.png';
	import faviconSvg from '$lib/assets/img/logos/logo.svg';
	import faviconIco32 from '$lib/assets/img/logos/logo32.ico';
	import faviconIco64 from '$lib/assets/img/logos/logo64.ico';
	import faviconIco128 from '$lib/assets/img/logos/logo128.ico';
	import {
		BackgroundPattern,
		Flex,
		Footer,
		Header,
		Heading,
		PresentationResponsiveContainer,
		Spacer,
		Text
	} from '$lib/components';
	import HeaderLogo from '$lib/components/presentation/Header/HeaderLogo.svelte';
	import { t, translations } from '$lib/translations/config';

	let { children, data } = $props();
	setTranslationsContext(translations);

	let initialized = $state(false);
	let splashPlayed = $state(false);

	const queryClient = new QueryClient();

	const doRefresh = async () => {
		if (data.refreshPresent && !data.user) {
			const res = await fetch('/api/auth/refresh', {
				method: 'POST'
			});
			if (res.ok) {
				await invalidate('app:user');
			} else {
				if (page.url.searchParams.has('next')) return;

				const next = encodeURIComponent(page.url.pathname + page.url.search);
				await goto(`/prihlaseni?next=${next}`, { replaceState: true });
			}
		}
	};

	onMount(async () => {
		if (!browser) return;

		//await doRefresh();

		initialized = true;
		setTimeout(() => {
			splashPlayed = true;
		}, 660);
	});

	afterNavigate(async () => {
		await invalidate('app:user');
		await doRefresh();
	});
</script>

<svelte:head>
	<meta name="keywords" content={$t('meta.keywords')} />
	<link rel="icon" href={faviconIco128} type="image/x-icon" sizes="128x128" />
	<link rel="icon" href={faviconIco64} type="image/x-icon" sizes="64x64" />
	<link rel="icon" href={faviconIco32} type="image/x-icon" sizes="32x32" />
	<link rel="icon" href={faviconSvg} type="image/svg+xml" />
	<link rel="apple-touch-icon" href={appleTouchIcon} />
	<link rel="manifest" href="/manifest.json" />
</svelte:head>

<div class="splash" class:initialized class:played={splashPlayed}>
	<img src={faviconSvg} alt="Logo" />
</div>

{#if initialized && splashPlayed}
	<div class="content">
		<Header />

		<QueryClientProvider client={queryClient}>
			{@render children?.()}
		</QueryClientProvider>

		<BackgroundPattern />
	</div>

	<Footer />
{/if}

<noscript>
	<Flex direction="col" style="height: 100dvh" justify="between">
		<PresentationResponsiveContainer>
			<header>
				<Flex>
					<HeaderLogo />
				</Flex>
			</header>

			<Flex direction="col" gap="m">
				<Spacer size="between-sections" />
				<Heading align="center" size={{ mobile: '2', tablet: '1', desktop: 'hero' }}>
					JavaScript je vypnutý
				</Heading>

				<Text align="center">
					Abychom vám mohli ukázat obsah a funkce našeho webu, potřebujeme, abyste si
					zapnuli JavaScript ve svém prohlížeči.
				</Text>

				<Spacer size="between-sections" />
			</Flex>
		</PresentationResponsiveContainer>
	</Flex>

	<style>
		header {
			padding: var(--spacing-l) 0;
			position: sticky;
			top: 0;
			z-index: var(--z-index-header);
			background-color: var(--color-background);
			opacity: 1;
		}

		.splash {
			display: none !important;
		}
	</style>
</noscript>

<style lang="scss">
	.content {
		position: relative;
		min-height: 100dvh;
		display: flex;
		flex-direction: column;
	}

	.splash {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: var(--color-light);
		display: flex;
		align-items: center;
		justify-content: center;
		pointer-events: none;
		z-index: 1000;
		transition: opacity 0.3s ease;

		img {
			width: 100px;
			transition:
				scale 0.3s ease,
				opacity 0.3s ease,
				translate 0.3s ease;
			opacity: 0;
			scale: 1;
			translate: 0 -20px;
		}

		&.initialized {
			img {
				opacity: 1;
				translate: 0 0;
			}
		}

		&.played {
			opacity: 0;

			img {
				scale: 1.5;
			}
		}
	}
</style>
