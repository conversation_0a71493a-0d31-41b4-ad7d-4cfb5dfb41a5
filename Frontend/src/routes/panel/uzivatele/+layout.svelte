<script lang="ts">
	import { UserPlus } from '@lucide/svelte';

	import { page } from '$app/state';
	import {
		Button,
		Flex,
		Heading,
		HorizontalTabNav,
		NavTab,
		PageTransition,
		Spacer
	} from '$lib/components';
	import { Can } from '$lib/permissions';

	let { children } = $props();

	const isActive = (href: string) => page.url.pathname === href;
</script>

<Flex direction="row" align="center">
	<Heading as="h1" size="2">Uživatelé</Heading>

	<Spacer direction="horizontal" size="m" />
	<Spacer grow="1" />

	<HorizontalTabNav>
		<Can subject="Student" action="read">
			<NavTab href="/panel/uzivatele/studenti" label="Studenti" />
		</Can>

		<Can subject="Teacher" action="read">
			<NavTab href="/panel/uzivatele/lektori" label="Lektoři" />
		</Can>

		<Can subject="Manager" action="read">
			<NavTab href="/panel/uzivatele/manageri" label="Manažeři" />
		</Can>

		<Can subject="Admin" action="read">
			<NavTab href="/panel/uzivatele/administratori" label="Administrátoři" />
		</Can>
	</HorizontalTabNav>

	<Spacer direction="horizontal" size="m" />
	<Button disabled variant="success" size="medium" TrailingIcon={UserPlus}>
		Přidat uživatele
	</Button>
</Flex>

<Spacer direction="vertical" size="xl" />

<PageTransition key="panel.users">
	{@render children()}
</PageTransition>
