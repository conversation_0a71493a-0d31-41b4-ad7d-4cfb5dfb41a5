<script lang="ts">
	import { exists } from '$lib/appmaxx/util';
	import { DataTable, Loader, Text } from '$lib/components';
	import DateCell from '$lib/components/panel/DataTable/cells/renderers/DateCell.svelte';
	import EmailCell from '$lib/components/panel/DataTable/cells/renderers/EmailCell.svelte';
	import PillCell from '$lib/components/panel/DataTable/cells/renderers/PillCell.svelte';
	import {
		createCellRenderer,
		type DataTableColumnDefinitions
	} from '$lib/components/panel/DataTable/DataTable';
	import UsersPageHeading from '$lib/components/panel/users/UsersPageHeading.svelte';
	import { getUserRoleColor, type UserRole } from '$lib/permissions';
	import type { Manager } from '$lib/services';
	import { userService } from '$lib/services';
	import { T, t } from '$lib/translations/config';

	import { ERR_PILL_COLOR, OK_PILL_COLOR } from '../constants';
	import LanguageCell from '$lib/components/panel/DataTable/cells/renderers/LanguageCell.svelte';

	const managersQuery = userService.listManagers();

	let columns = $state<DataTableColumnDefinitions<Manager>[]>([
		{
			accessorKey: 'firstName',
			header: $t('users.dataGrid.firstName')
		},
		{
			accessorKey: 'lastName',
			header: $t('users.dataGrid.lastName')
		},
		{
			accessorKey: 'email',
			header: $t('users.dataGrid.email'),
			cell: ({ getValue }) => createCellRenderer(EmailCell, { email: getValue() as string })
		},
		{
			accessorKey: 'role',
			header: $t('users.dataGrid.role'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getUserRoleColor(getValue() as UserRole),
					label: getValue() as string
				})
		},
		{
			accessorKey: 'lang',
			header: $t('users.dataGrid.language'),
			cell: ({ getValue }) => createCellRenderer(LanguageCell, { language: getValue() as string })
		},
		{
			accessorKey: 'emailVerifiedAt',
			header: $t('users.dataGrid.emailVerified'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getValue() ? OK_PILL_COLOR : ERR_PILL_COLOR,
					label: getValue() ? $t('users.emailVerified.yes') : $t('users.emailVerified.no')
				})
		},
		{
			accessorKey: 'createdAt',
			header: $t('users.dataGrid.createdAt'),
			cell: ({ getValue }) =>
				createCellRenderer(DateCell, {
					date: exists(getValue()) ? new Date(getValue() as string) : null
				})
		},
		{
			accessorKey: 'archivedAt',
			header: $t('users.dataGrid.status'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getValue() ? ERR_PILL_COLOR : OK_PILL_COLOR,
					label: getValue() ? $t('users.status.archived') : $t('users.status.active')
				})
		}
	]);
</script>

<UsersPageHeading title={$t('users.managers.title')} queryKey={['users', 'managers']} />

{#if $managersQuery.isPending}
	<Loader fillPage />
{:else if $managersQuery.error}
	<Text><T key="users.errors.loadingManagers" />: {$managersQuery.error.message}</Text>
{:else if $managersQuery.data}
	<DataTable data={$managersQuery.data} {columns} />
{/if}
