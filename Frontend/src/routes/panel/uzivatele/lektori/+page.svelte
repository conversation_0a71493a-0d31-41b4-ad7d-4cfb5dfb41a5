<script lang="ts">
	import { exists } from '$lib/appmaxx/util';
	import { type DataTableColumnDefinitions, MetaSeo } from '$lib/components';
	import { DataTable, Loader, Text } from '$lib/components';
	import DateCell from '$lib/components/panel/DataTable/cells/renderers/DateCell.svelte';
	import EmailCell from '$lib/components/panel/DataTable/cells/renderers/EmailCell.svelte';
	import LanguageCell from '$lib/components/panel/DataTable/cells/renderers/LanguageCell.svelte';
	import PillCell from '$lib/components/panel/DataTable/cells/renderers/PillCell.svelte';
	import { createCellRenderer } from '$lib/components/panel/DataTable/DataTable';
	import UsersPageHeading from '$lib/components/panel/users/UsersPageHeading.svelte';
	import { getUserRoleColor, getUserRoleTranslationKey, type UserRole } from '$lib/permissions';
	import type { Teacher } from '$lib/services';
	import { userService } from '$lib/services';
	import { T, t } from '$lib/translations/config';

	import { ERR_PILL_COLOR, OK_PILL_COLOR } from '../constants';

	const teachersQuery = userService.listTeachers();

	let columns = $state<DataTableColumnDefinitions<Teacher>[]>([
		{
			accessorKey: 'firstName',
			header: $t('users.dataGrid.firstName')
		},
		{
			accessorKey: 'lastName',
			header: $t('users.dataGrid.lastName')
		},
		{
			accessorKey: 'email',
			header: $t('users.dataGrid.email'),
			cell: ({ getValue }) => createCellRenderer(EmailCell, { email: getValue() as string })
		},
		{
			accessorKey: 'role',
			header: $t('users.dataGrid.role'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getUserRoleColor(getValue() as UserRole),
					label: $t(getUserRoleTranslationKey(getValue() as UserRole))
				})
		},
		{
			accessorKey: 'lang',
			header: $t('users.dataGrid.language'),
			cell: ({ getValue }) =>
				createCellRenderer(LanguageCell, { language: getValue() as string })
		},
		{
			accessorKey: 'emailVerifiedAt',
			header: $t('users.dataGrid.emailVerified'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getValue() ? OK_PILL_COLOR : ERR_PILL_COLOR,
					label: getValue() ? $t('users.emailVerified.yes') : $t('users.emailVerified.no')
				})
		},
		{
			accessorKey: 'createdAt',
			header: $t('users.dataGrid.createdAt'),
			cell: ({ getValue }) =>
				createCellRenderer(DateCell, {
					date: exists(getValue()) ? new Date(getValue() as string) : null
				})
		},
		{
			accessorKey: 'phone',
			header: $t('users.dataGrid.phone'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value || $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'description',
			header: $t('users.dataGrid.description'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;
				return value
					? value.length > 50
						? `${value.substring(0, 50)}...`
						: value
					: $t('users.common.notAvailable');
			}
		},
		{
			accessorKey: 'wageTax',
			header: $t('users.dataGrid.wageTax'),
			cell: ({ getValue }) => {
				const value = getValue() as number | null;
				return value !== null ? `${value}%` : '';
			}
		},
		{
			accessorKey: 'archivedAt',
			header: $t('users.dataGrid.status'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getValue() ? ERR_PILL_COLOR : OK_PILL_COLOR,
					label: getValue() ? $t('users.status.archived') : $t('users.status.active')
				})
		}
	]);
</script>

<MetaSeo title={$t('users.teachers.pageTitle')} />

<UsersPageHeading title={$t('users.teachers.title')} queryKey={['users', 'teachers']} />

{#if $teachersQuery.isPending}
	<Loader fillPage />
{:else if $teachersQuery.error}
	<Text><T key="users.errors.loadingTeachers" />: {$teachersQuery.error.message}</Text>
{:else if $teachersQuery.data}
	<DataTable data={$teachersQuery.data} {columns} />
{/if}
