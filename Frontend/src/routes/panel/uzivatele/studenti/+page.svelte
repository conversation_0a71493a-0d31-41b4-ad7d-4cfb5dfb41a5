<script lang="ts">
	import { exists } from '$lib/appmaxx/util/index.js';
	import { DataTable, Loader, Text } from '$lib/components';
	import DateCell from '$lib/components/panel/DataTable/cells/renderers/DateCell.svelte';
	import EmailCell from '$lib/components/panel/DataTable/cells/renderers/EmailCell.svelte';
	import PillCell from '$lib/components/panel/DataTable/cells/renderers/PillCell.svelte';
	import {
		createCellRenderer,
		type DataTableColumnDefinitions
	} from '$lib/components/panel/DataTable/DataTable';
	import UsersPageHeading from '$lib/components/panel/users/UsersPageHeading.svelte';
	import { getUserRoleColor, type UserRole } from '$lib/permissions';
	import type { Student } from '$lib/services';
	import { userService } from '$lib/services';
	import { T, t } from '$lib/translations/config';

	import { ERR_PILL_COLOR, OK_PILL_COLOR } from '../constants';
	import LanguageCell from '$lib/components/panel/DataTable/cells/renderers/LanguageCell.svelte';

	const studentsQuery = userService.listStudents();

	let columns = $state<DataTableColumnDefinitions<Student>[]>([
		{
			accessorKey: 'firstName',
			header: $t('users.dataGrid.firstName')
		},
		{
			accessorKey: 'lastName',
			header: $t('users.dataGrid.lastName')
		},
		{
			accessorKey: 'email',
			header: $t('users.dataGrid.email'),
			cell: ({ getValue }) => createCellRenderer(EmailCell, { email: getValue() as string })
		},
		{
			accessorKey: 'emailStudent',
			header: $t('users.dataGrid.emailStudent'),
			cell: ({ getValue }) => createCellRenderer(EmailCell, { email: getValue() as string })
		},
		{
			accessorKey: 'role',
			header: $t('users.dataGrid.role'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getUserRoleColor(getValue() as UserRole),
					label: getValue() as string
				})
		},
		{
			accessorKey: 'lang',
			header: $t('users.dataGrid.language'),
			cell: ({ getValue }) => createCellRenderer(LanguageCell, { language: getValue() as string })
		},
		{
			accessorKey: 'emailVerifiedAt',
			header: $t('users.dataGrid.emailVerified'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getValue() ? OK_PILL_COLOR : ERR_PILL_COLOR,
					label: getValue() ? $t('users.emailVerified.yes') : $t('users.emailVerified.no')
				})
		},
		{
			accessorKey: 'createdAt',
			header: $t('users.dataGrid.createdAt'),
			cell: ({ getValue }) =>
				createCellRenderer(DateCell, {
					date: exists(getValue()) ? new Date(getValue() as string) : null
				})
		},
		{
			accessorKey: 'billingFirstName',
			header: $t('users.dataGrid.billingFirstName')
		},
		{
			accessorKey: 'billingLastName',
			header: $t('users.dataGrid.billingLastName')
		},
		{
			accessorKey: 'billingAddress',
			header: $t('users.dataGrid.billingAddress')
		},
		{
			accessorKey: 'billingCity',
			header: $t('users.dataGrid.billingCity')
		},
		{
			accessorKey: 'billingZip',
			header: $t('users.dataGrid.billingZip')
		},
		{
			accessorKey: 'billingPhone',
			header: $t('users.dataGrid.billingPhone')
		},
		{
			accessorKey: 'billingEmail',
			header: $t('users.dataGrid.billingEmail'),
			cell: ({ getValue }) => {
				const value = getValue() as string | null;

				if (!value) {
					return '';
				}

				return createCellRenderer(EmailCell, { email: value });
			}
		},
		{
			accessorKey: 'archivedAt',
			header: $t('users.dataGrid.status'),
			cell: ({ getValue }) =>
				createCellRenderer(PillCell, {
					hexColor: getValue() ? ERR_PILL_COLOR : OK_PILL_COLOR,
					label: getValue() ? $t('users.status.archived') : $t('users.status.active')
				})
		}
	]);
</script>

<UsersPageHeading title={$t('users.students.title')} queryKey={['users', 'students']} />

{#if $studentsQuery.isPending}
	<Loader fillPage />
{:else if $studentsQuery.error}
	<Text>
		<T key="users.errors.loadingStudents" />
		: {$studentsQuery.error.message}</Text
	>
{:else if $studentsQuery.data}
	<DataTable data={$studentsQuery.data} {columns} />
{/if}
