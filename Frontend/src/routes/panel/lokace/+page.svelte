<script lang="ts">
	import { DataTable, Loader, Text } from '$lib/components';
	import type { DataTableColumnDefinitions } from '$lib/components/panel/DataTable/DataTable';
	import { type Location, locationService } from '$lib/services';

	const locationsQuery = locationService.listLocations();

	let columns = $state<DataTableColumnDefinitions<Location>[]>([
		{
			accessorKey: 'id',
			header: 'ID',
			cell: ({ getValue }) => `#${getValue()}`
		}
	]);
</script>

{#if $locationsQuery.isPending}
	<Loader fillPage />
{:else if $locationsQuery.error}
	<Text>Error loading locations: {$locationsQuery.error.message}</Text>
{:else if $locationsQuery.data}
	<DataTable data={$locationsQuery.data} {columns} />
{/if}
