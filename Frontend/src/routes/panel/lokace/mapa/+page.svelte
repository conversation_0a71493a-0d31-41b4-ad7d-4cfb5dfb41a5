<script lang="ts">
	import { Flex, Heading, LocationsMap, Spacer } from '$lib/components';
</script>

<Heading as="h2" size="3">Mapa lokací</Heading>
<Spacer direction="vertical" size="l" />

<Flex class="panel-locations-map-container" direction="col" flex="1">
	<LocationsMap withoutSearch withoutPageOverflow />
</Flex>

<style lang="scss">
	:global {
		.panel-locations-map-container .locations-map-card {
			flex: 1;

			.map {
				height: 100%;
			}
		}
	}
</style>
