<script lang="ts">
	import { Button, DataTable, Flex, Heading, Loader, Spacer, Text } from '$lib/components';
	import type { DataTableColumnDefinitions } from '$lib/components/panel/DataTable/DataTable';
	import { type Lecture, lectureService } from '$lib/services';

	const lecturesQuery = lectureService.listLectures();

	let columns = $state<DataTableColumnDefinitions<Lecture>[]>([
		{
			accessorKey: 'id',
			header: 'ID',
			cell: ({ getValue }) => `#${getValue()}`
		}
	]);
</script>

<Flex direction="row" align="center">
	<Heading as="h1" size="2">Lekce</Heading>
	<Spacer grow="1" />
	<Button disabled variant="success">Vytvořit lekci</Button>
</Flex>

<Spacer direction="vertical" size="l" />

{#if $lecturesQuery.isPending}
	<Loader fillPage />
{:else if $lecturesQuery.error}
	<Text>Error loading lectures: {$lecturesQuery.error.message}</Text>
{:else if $lecturesQuery.data}
	<DataTable data={$lecturesQuery.data} {columns} />
{/if}
