import { Prisma } from '@prisma/client';
import { getStringFilter } from 'src/common/utils';
import { indexBy } from 'src/utils';

import { ListTeachersRequestBody } from '../../schemas/listTeachers';

export const transformFiltering = (
	args: NonNullable<ListTeachersRequestBody['filters']>
): Prisma.TeacherWhereInput => {
	const { email, firstName, lastName, phone, description, wageTax } = indexBy(args, 'field');

	const teacherWhereInput: Prisma.TeacherWhereInput = {
		phone: getStringFilter(phone),
		description: getStringFilter(description),
		wageTax: getStringFilter(wageTax)
	};
	const userWhereInput: Prisma.UserWhereInput = {
		email: getStringFilter(email),
		firstName: getStringFilter(firstName),
		lastName: getStringFilter(lastName)
	};
	const someTeacherFilter = Object.values(teacherWhereInput).some((v) => v !== undefined);
	const someUserFilter = Object.values(userWhereInput).some((v) => v !== undefined);

	return {
		...(someTeacherFilter ? teacherWhereInput : {}),
		...(someUserFilter ? { user: userWhereInput } : {})
	};
};
