import { Module } from '@nestjs/common';
import { UsersModule } from '@modules/users/users.module';
import { PrismaModule } from '@/infra/prisma/prisma.module';
import { AdminsRepo } from '@modules/admins/admins.repo';
import { AdminsPrismaRepo } from '@modules/admins/admins.prisma.repo';
import { CreateAdminUseCase, GetAdminUseCase, ListAdminsUseCase } from '@modules/admins/useCases';
import { AdminsController } from '@modules/admins/controllers/admins.controller';

@Module({
	imports: [UsersModule, PrismaModule],
	providers: [
		{ provide: AdminsRepo, useClass: AdminsPrismaRepo },
		ListAdminsUseCase,
		CreateAdminUseCase,
		GetAdminUseCase
	],
	controllers: [AdminsController],
	exports: [AdminsRepo]
})
export class AdminsModule {}
