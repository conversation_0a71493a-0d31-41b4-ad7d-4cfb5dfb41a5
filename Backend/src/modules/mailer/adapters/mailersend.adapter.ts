import { <PERSON><PERSON><PERSON>ara<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Recipient, Sender } from 'mailersend';

import { IMailProvider, SendMailOptions } from '../mail.interface';

export class MailerSendAdapter implements IMailProvider {
	private client: MailerSend;
	private defaultFrom: Sender;

	constructor(apiKey: string, fromEmail: string, fromName?: string) {
		this.client = new MailerSend({ apiKey });
		this.defaultFrom = new Sender(fromEmail, fromName ?? undefined);
	}

	async send(opts: SendMailOptions): Promise<void> {
		try {
			const toArr = Array.isArray(opts.to) ? opts.to : [opts.to];
			const recipients = toArr.map((t) => new Recipient(t.email, t.name));
			const emailParams = new EmailParams()
				.setFrom(opts.from ? new Sender(opts.from.email, opts.from.name) : this.defaultFrom)
				.setTo(recipients)
				.setSubject(opts.subject)
				.setHtml(opts.html)
				.setText(opts.text ?? '')
				.setTags(opts.tags ?? []);
			await this.client.email.send(emailParams);
		} catch (e) {
			console.log(e);
		}
	}
}
