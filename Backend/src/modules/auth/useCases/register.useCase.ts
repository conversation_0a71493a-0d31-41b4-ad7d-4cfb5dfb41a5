import { EmailRegisteredError } from '@common/errors';
import { getClientIp, getUserAgent } from '@common/http/client';
import { setCookies } from '@common/http/cookies';
import { RegisterValues } from '@common/schemas';
import { AUTH_MODULE_OPTIONS, AuthModuleOptions } from '@modules/auth/auth.config';
import { EmailVerificationTokensService } from '@modules/emailVerification/emailVerificationTokens.service';
import { MailerService } from '@modules/mailer/mailer.service';
import { RefreshTokensRepo } from '@modules/refreshTokens/refreshTokens.repo';
import { SessionsRepo } from '@modules/sessions/sessions.repo';
import { StudentsRepo } from '@modules/students/students.repo';
import { TokensService } from '@modules/tokens/tokens.service';
import { UsersRepo } from '@modules/users/users.repo';
import { Inject, Injectable } from '@nestjs/common';
import * as argon2 from 'argon2';
import { randomUUID } from 'crypto';
import type { Request, Response } from 'express';

import { TransactionService } from '@/infra/prisma/transaction.service';
@Injectable()
export class RegisterUseCase {
	constructor(
		private users: UsersRepo,
		private students: StudentsRepo,
		private sessions: SessionsRepo,
		private tokens: TokensService,
		private rts: RefreshTokensRepo,
		private transaction: TransactionService,
		private mailer: MailerService,
		private emailVerificationTokensService: EmailVerificationTokensService,
		@Inject(AUTH_MODULE_OPTIONS) private opts: Required<AuthModuleOptions>
	) {}

	async execute(
		{ email, emailStudent, firstName, lastName, password }: RegisterValues,
		req: Request
	) {
		if (await this.users.emailExists(email)) throw new EmailRegisteredError();

		const { at, rt, user } = await this.transaction.run(async (tx) => {
			const passwordHash = await argon2.hash(password, {
				type: argon2.argon2id,
				timeCost: 3,
				memoryCost: 65_536,
				parallelism: 1
			});

			const user = await this.users.create(
				{
					id: randomUUID(),
					firstName,
					lastName,
					email,
					passwordHash,
					role: 'student'
					//TODO: add
					//1. number
					//2. lang
				},
				tx
			);
			await this.students.create(
				{
					userId: user.id,
					emailStudent
				},
				tx
			);

			const sid = randomUUID();
			await this.sessions.create(
				{
					id: sid,
					userId: user.id,
					mfaLevel: 0,
					userAgent: getUserAgent(req)?.slice(0, 512),
					ip: getClientIp(req)
				},
				tx
			);

			const at = await this.tokens.signAT(
				{
					sub: user.id,
					sid,
					permsVer: user.permsVersion,
					role: 'student',
					firstname: user.firstName,
					lastname: user.lastName,
					email: user.email
				},
				this.opts.accessTokenTtlSec
			);
			const family = randomUUID();
			const rt = await this.tokens.signRT(
				{ sub: user.id, sid, family, rot: 0 },
				this.opts.refreshTokenTtlSec
			);
			await this.rts.insert(
				{
					jti: rt.jti,
					userId: user.id,
					sessionId: sid,
					familyId: family,
					rot: 0,
					expiresAt: new Date(Date.now() + rt.maxAgeSec * 1000)
				},
				tx
			);

			return { rt, at, user };
		});

		await this.emailVerificationTokensService.request({ userId: user.id });

		return {
			setCookies: (res: Response) => {
				setCookies(res, [
					{
						name: this.opts.refreshTokenCookieName,
						value: rt.token,
						options: {
							httpOnly: true,
							path: this.opts.refreshTokenCookiePath,
							maxAge: rt.maxAgeSec
						}
					},
					{
						name: this.opts.refreshTokenPresentCookieName,
						value: '1',
						options: {
							httpOnly: true,
							path: this.opts.refreshTokenPresentCookiePath,
							maxAge: rt.maxAgeSec,
							sameSite: 'lax'
						}
					},
					{
						name: this.opts.accessTokenCookieName,
						value: at.token,
						options: {
							httpOnly: true,
							path: this.opts.accessTokenCookiePath,
							maxAge: at.maxAgeSec,
							sameSite: 'lax'
						}
					}
				]);
			}
		};
	}
}
