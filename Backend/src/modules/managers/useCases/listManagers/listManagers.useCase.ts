import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

import { ManagersRepo } from '../../managers.repo';
import { ListManagersRequestBody } from '../../schemas/listManagers';
import { transformFiltering } from './transformFiltering';
import { transformSorting } from './transformSorting';

@Injectable()
export class ListManagersUseCase {
	constructor(private managers: ManagersRepo) {}
	async execute({ skip, filters, sort, take }: ListManagersRequestBody) {
		const findArgs: Prisma.ManagerFindManyArgs = {};

		if (sort) {
			findArgs.orderBy = transformSorting(sort);
		}

		if (filters) {
			findArgs.where = transformFiltering(filters);
		}

		return this.managers.list({
			skip,
			take,
			...findArgs
		});
	}
}
