import { Module } from '@nestjs/common';
import { PrismaModule } from 'src/infra/prisma/prisma.module';

import { MailerModule } from '../mailer/mailer.module';
import { MailerService } from '../mailer/mailer.service';
import { NewsletterController } from './newsletter.controller';
import { SubscribersPrismaRepo } from './subscribers.prisma.repo';
import { SubscribersRepo } from './subscribers.repo';

@Module({
	imports: [MailerModule, PrismaModule],
	controllers: [NewsletterController],
	providers: [{ provide: SubscribersRepo, useClass: SubscribersPrismaRepo }, MailerService],
	exports: [SubscribersRepo]
})
export class NewsletterModule {}
