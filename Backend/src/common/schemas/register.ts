import { z } from 'zod/v4';

export const RegisterSchema = z
	.object({
		firstName: z
			.string({ message: 'global.errors.validation.required' })
			.trim()
			.min(1, { message: 'global.errors.validation.required' })
			.default(''),
		lastName: z
			.string({ message: 'global.errors.validation.required' })
			.trim()
			.min(1, { message: 'global.errors.validation.required' })
			.default(''),

		email: z
			.email({ message: 'global.errors.validation.string_email' })
			.trim()
			.toLowerCase()
			.default(''),

		emailStudent: z
			.email({ message: 'global.errors.validation.string_email' })
			.trim()
			.toLowerCase()
			.default(''),

		password: z
			.string({ message: 'global.errors.validation.required' })
			.min(8, { message: 'global.errors.validation.password_min' })
			.default(''),

		passwordConfirm: z
			.string({ message: 'global.errors.validation.required' })
			.min(1, { message: 'global.errors.validation.required' })
			.default(''),

		consent: z
			.boolean()
			.refine((consent) => consent, {
				message: 'global.errors.validation.required'
			})
			.default(false)
	})
	.refine((data) => data.password === data.passwordConfirm, {
		path: ['passwordConfirm'],
		message: 'global.errors.validation.password_mismatch'
	});

export type RegisterInput = z.input<typeof RegisterSchema>;
export type RegisterValues = z.output<typeof RegisterSchema>;
