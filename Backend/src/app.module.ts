import { loadAndValidateEnv } from '@config/env';
import { AdminsModule } from '@modules/admins/admins.module';
import { AbilityModule } from '@modules/auth/ability/ability.module';
import { AuthModule } from '@modules/auth/auth.module';
import { AccessGuard } from '@modules/auth/security/guards/access.guard';
import { BecomeTeacherModule } from '@modules/becomeTeacher/becomeTeacher.module';
import { JwksModule } from '@modules/jwks/jwks.module';
import { LecturesModule } from '@modules/lectures/lectures.module';
import { LinkModule } from '@modules/link/link.module';
import { LocationsModule } from '@modules/locations/locations.module';
import { MailerModule } from '@modules/mailer/mailer.module';
import { ManagersModule } from '@modules/managers/managers.module';
import { NewsletterController } from '@modules/newsletter/newsletter.controller';
import { NewsletterModule } from '@modules/newsletter/newsletter.module';
import { PasswordResetTokensModule } from '@modules/passwordResetTokens/passwordResetTokens.module';
import { RefreshTokensModule } from '@modules/refreshTokens/refreshTokens.module';
import { SessionsModule } from '@modules/sessions/sessions.module';
import { StudentsModule } from '@modules/students/students.module';
import { TeachersModule } from '@modules/teachers/teachers.module';
import { TokensModule } from '@modules/tokens/tokens.module';
import { CreditTransactionsModule } from '@modules/transactions/transactions.module';
import { UsersModule } from '@modules/users/users.module';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { ThrottlerModule } from '@nestjs/throttler';

import { AppController } from '@/app.controller';
import { AppService } from '@/app.service';
import { PrismaModule } from '@/infra/prisma/prisma.module';

import { StorageModule } from './modules/storage/storage.module';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
			load: [() => loadAndValidateEnv()]
		}),
		ThrottlerModule.forRoot([
			{
				ttl: 60, // time window (s)
				limit: 30 // max requests per window
			}
		]),
		PrismaModule,
		StorageModule,
		JwksModule,
		TokensModule,
		UsersModule,
		StudentsModule,
		LecturesModule,
		TeachersModule,
		CreditTransactionsModule,
		ManagersModule,
		AdminsModule,
		SessionsModule,
		RefreshTokensModule,
		PasswordResetTokensModule,
		AuthModule.forRoot(),
		AbilityModule,
		MailerModule,
		NewsletterModule,
		BecomeTeacherModule,
		LocationsModule,
		LinkModule
	],
	controllers: [AppController, NewsletterController],
	providers: [AppService, { provide: APP_GUARD, useClass: AccessGuard }]
})
export class AppModule {}
