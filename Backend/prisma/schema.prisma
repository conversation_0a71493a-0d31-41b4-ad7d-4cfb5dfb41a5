// This is your Prisma schema file,
// learn more: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Lang {
  cs
  ua
}

enum Roles {
  student
  teacher
  manager
  admin
}

enum LocationPurpose {
  prijimacky_nanecisto
  prezenc<PERSON>_doucovani
}

model User {
  id                        String                     @id @default(uuid()) @db.Uuid
  firstName                 String
  lastName                  String
  email                     String                     @unique
  role                      Roles                      @default(student)
  passwordHash              String
  permsVersion              Int                        @default(1)
  createdAt                 DateTime                   @default(now()) @db.Timestamptz
  emailVerifiedAt           DateTime?                  @db.Timestamptz
  passwordChangedAt         DateTime?                  @db.Timestamptz
  lang                      Lang                       @default(cs)
  emailVerificationRequests EmailVerificationRequest[]
  passwordResetTokens       PasswordResetToken[]
  refreshTokens             RefreshToken[]
  sessions                  Session[]
  emailVerificationTokens   EmailVerificationToken[]
  admins                    Admin[]
  managers                  Manager[]
  students                  Student[]
  teachers                  Teacher[]
  canceledLectures          Lecture[]                  @relation("Lecture_CancelledBy")
  createdLectures           Lecture[]                  @relation("Lecture_CreatedBy")
  updatedLectures           Lecture[]                  @relation("Lecture_UpdatedBy")

  @@index([lang])
}

model EmailVerificationRequest {
  id     String   @id @default(uuid()) @db.Uuid
  userId String   @db.Uuid
  user   User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  sentAt DateTime @default(now()) @db.Timestamptz

  @@index([sentAt])
}

model PasswordResetToken {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String    @db.Uuid
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  tokenHash String
  expiresAt DateTime  @db.Timestamptz
  usedAt    DateTime? @db.Timestamptz
  revokedAt DateTime? @db.Timestamptz
  createdAt DateTime  @default(now()) @db.Timestamptz
  requestIp String?
  userAgent String?

  @@unique([userId, expiresAt])
  @@unique([tokenHash])
  @@index([expiresAt])
}

model RefreshToken {
  jti        String    @id @default(uuid()) @db.Uuid
  userId     String    @db.Uuid
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  sessionId  String    @db.Uuid
  session    Session   @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  familyId   String    @db.Uuid
  rot        Int       @default(0)
  createdAt  DateTime  @default(now()) @db.Timestamptz
  usedAt     DateTime? @db.Timestamptz
  revokedAt  DateTime? @db.Timestamptz
  expiresAt  DateTime? @db.Timestamptz
  replacedBy String?   @db.Uuid

  @@index([familyId])
  @@index([sessionId])
}

model Session {
  id            String         @id @default(uuid()) @db.Uuid
  userId        String         @db.Uuid
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  userAgent     String?
  did           String?
  ip            String?
  mfaLevel      Int            @default(0)
  createdAt     DateTime       @default(now()) @db.Timestamptz
  lastSeen      DateTime       @default(now()) @db.Timestamptz
  revokedAt     DateTime?      @db.Timestamptz
  refreshTokens RefreshToken[]

  @@index([userId])
}

model NewsletterSubscriber {
  id             String    @id @default(uuid()) @db.Uuid
  email          String    @unique
  status         String    @default("active") // active | unsubscribed | blocked
  unsubscribedAt DateTime? @db.Timestamptz
  lastIp         String?   @db.Inet
  lastUserAgent  String?
  subscribedAt   DateTime  @default(now()) @db.Timestamptz
  lang           Lang      @default(cs)

  @@index([status])
  @@index([lang])
}

model EmailVerificationToken {
  id        String    @id @default(uuid()) @db.Uuid
  userId    String    @db.Uuid
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  tokenHash String
  expiresAt DateTime  @db.Timestamptz
  usedAt    DateTime? @db.Timestamptz
  revokedAt DateTime? @db.Timestamptz
  createdAt DateTime  @default(now()) @db.Timestamptz

  @@unique([userId, expiresAt])
  @@unique([tokenHash])
  @@index([expiresAt])
}

model Admin {
  id         String    @id @default(uuid()) @db.Uuid
  userId     String    @db.Uuid
  user       User      @relation(fields: [userId], references: [id], onDelete: NoAction)
  archivedAt DateTime? @db.Timestamptz

  @@index([userId])
}

model Manager {
  id         String    @id @default(uuid()) @db.Uuid
  userId     String    @db.Uuid
  user       User      @relation(fields: [userId], references: [id], onDelete: NoAction)
  archivedAt DateTime? @db.Timestamptz

  locations Location[]

  @@index([userId])
}

model Student {
  id           String @id @default(uuid()) @db.Uuid
  userId       String @db.Uuid
  user         User   @relation(fields: [userId], references: [id], onDelete: NoAction)
  emailStudent String

  billingFirstName String?
  billingLastName  String?
  billingAddress   String?
  billingCity      String?
  billingZip       String?
  billingPhone     String?
  billingEmail     String?

  archivedAt   DateTime?            @db.Timestamptz

  wallet       CreditWallet?
  reservations LectureReservation[]
}

model Teacher {
  id     String @id @default(uuid()) @db.Uuid
  userId String @db.Uuid
  user   User   @relation(fields: [userId], references: [id], onDelete: NoAction)

  phone       String?
  description String?
  wageTax     Decimal? @db.Decimal(5, 2)

  archivedAt DateTime? @db.Timestamptz

  locations Location[]
  lectures  Lecture[]
}

model Location {
  id String @id @default(uuid()) @db.Uuid

  name        String
  address     String
  description String
  rentalCost  Decimal?        @db.Decimal(10, 2)
  capacity    Int
  purpose     LocationPurpose

  imgKey String?

  managers Manager[]
  teachers Teacher[]
  lectures Lecture[]
}

model BecomeTeacher {
  id        String   @id @default(uuid()) @db.Uuid
  name      String
  school    String
  phone     String
  email     String   @unique
  message   String
  createdAt DateTime @default(now()) @db.Timestamptz
}

enum LectureType {
  online
  onsite
  test
}

model Lecture {
  id          String   @id @default(uuid()) @db.Uuid
  createdAt   DateTime @default(now()) @db.Timestamptz
  updatedAt   DateTime @updatedAt @db.Timestamptz
  createdById String   @db.Uuid
  createdBy   User     @relation(fields: [createdById], references: [id], onDelete: NoAction, onUpdate: Cascade, name: "Lecture_CreatedBy")
  updatedById String   @db.Uuid
  updatedBy   User     @relation(fields: [updatedById], references: [id], onDelete: NoAction, onUpdate: Cascade, name: "Lecture_UpdatedBy")

  groupId   String      @db.Uuid
  type      LectureType
  lang      Lang
  name      String
  price     Decimal     @db.Decimal(10, 2)
  dateStart DateTime    @db.Timestamptz
  dateEnd   DateTime    @db.Timestamptz
  notes     String?
  capacity  Int?

  cancelledAt   DateTime? @db.Timestamptz
  cancelledById String?   @db.Uuid
  cancelledBy   User?     @relation(fields: [cancelledById], references: [id], onDelete: SetNull, onUpdate: Cascade, name: "Lecture_CancelledBy")

  locationId        String?              @db.Uuid
  location          Location?            @relation(fields: [locationId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  teacherId         String?              @db.Uuid
  teacher           Teacher?             @relation(fields: [teacherId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  teacherAssignedAt DateTime?            @db.Timestamptz
  reservations      LectureReservation[]

  @@index([type])
  @@index([locationId])
  @@index([groupId])
}

model LectureReservation {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @db.Timestamptz

  lectureId String  @db.Uuid
  lecture   Lecture @relation(fields: [lectureId], references: [id], onDelete: Cascade)
  studentId String  @db.Uuid
  student   Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  deleted DateTime? @db.Timestamptz

  @@unique([lectureId, studentId])
  @@index([studentId])
  @@index([lectureId])
}

model CreditWallet {
  id        String   @id @default(uuid()) @db.Uuid

  studentId String   @db.Uuid
  student   Student  @relation(fields: [studentId], references: [id], onDelete: NoAction)
  balance   Decimal  @default(0) @db.Decimal(10, 2)
  updatedAt DateTime @updatedAt @db.Timestamptz
  createdAt DateTime @default(now()) @db.Timestamptz

  transactions CreditTransaction[]

  @@index([studentId])
  @@unique([studentId])
}

enum CreditTransactionReason {
  manual_adjustment
  lecture_cancellation
  credit_purchase
  lecture_purchase
}
enum CreditTransactionDirection {
  credit
  debit
}

model CreditTransaction {
  id             String       @id @default(uuid()) @db.Uuid
  walletId       String       @db.Uuid
  wallet         CreditWallet @relation(fields: [walletId], references: [id], onDelete: NoAction)
  amount         Decimal      @db.Decimal(10, 2)
  balanceAfter   Decimal      @db.Decimal(10, 2)
  reason         CreditTransactionReason
  direction      CreditTransactionDirection
  reasonMetadata String?
  createdAt      DateTime     @default(now()) @db.Timestamptz

  @@index([walletId])
}