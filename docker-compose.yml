name: "dej-prijimacky-prod"

services:
  postgres:
    image: postgres:17-alpine
    container_name: prod-db
    env_file: .env
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - intranet

  backend:
    build:
      context: .
      dockerfile: .docker/Backend/Dockerfile
    container_name: prod-backend
    env_file: .env
    environment:
      NODE_ENV: production
      PORT: 3000
    depends_on:
      postgres:
        condition: service_started
    tmpfs:
      - /tmp
    networks:
      - intranet

  frontend:
    build:
      context: .
      dockerfile: .docker/Frontend/Dockerfile
    container_name: prod-frontend
    env_file: .env
    environment:
      NODE_ENV: production
      HOST: 0.0.0.0
      PORT: 3000
    depends_on:
      - backend
    networks:
      - intranet

  nginx:
    image: nginx:1.27-alpine
    container_name: prod-nginx
    expose:
      - "80"
      - "443"
    depends_on:
      - frontend
      - backend
    volumes:
      - ./.docker/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.web.rule=Host(`dej-prijimacky.cz`) || Host(`www.dej-prijimacky.cz`)"
      - "traefik.http.routers.web.entrypoints=websecure"
      - "traefik.http.routers.web.tls.certresolver=le"
    networks:
      - intranet

  traefik:
    image: traefik:v3.1
    command:
      # Discover Docker services and their labels
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"

      # Entrypoints
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"

      # Redirect HTTP -> HTTPS
      - "--entrypoints.web.http.redirections.entryPoint.to=websecure"
      - "--entrypoints.web.http.redirections.entryPoint.scheme=https"

      # Let's Encrypt (HTTP-01 challenge)
      - "--certificatesresolvers.le.acme.httpchallenge=true"
      - "--certificatesresolvers.le.acme.httpchallenge.entrypoint=web"
      - "--certificatesresolvers.le.acme.email=<EMAIL>"
      - "--certificatesresolvers.le.acme.storage=/letsencrypt/acme.json"

      # (Optional) dashboard on :8080, protected by a simple password
      # - "--api.dashboard=true"
    ports:
      - "80:80"
      - "443:443"
      # - "8080:8080"  # if you enable the dashboard
    volumes:
      - "traefik_letsencrypt:/letsencrypt"
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
    networks:
      - intranet

networks:
  intranet:
    driver: bridge

volumes:
  pgdata:
  traefik_letsencrypt: